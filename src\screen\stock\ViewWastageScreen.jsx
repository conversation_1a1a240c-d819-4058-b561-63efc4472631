import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { Dropdown } from 'react-native-element-dropdown';
import { BLEPrinter } from 'react-native-thermal-receipt-printer';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchWastageDetail } from '../../apiHandling/StockAPI/fetchWastageDetailAPI';

const ViewWastageScreen = ({ route }) => {
    const navigation = useNavigation();
    const { wastageId } = route.params;

    // State for wastage data
    const [wastageData, setWastageData] = useState(null);
    const [branchData, setBranchData] = useState(null);
    const [loading, setLoading] = useState(true);

    // Bluetooth printer state
    const [availablePrinters, setAvailablePrinters] = useState([]);
    const [selectedPrinter, setSelectedPrinter] = useState(null);
    const [isScanning, setIsScanning] = useState(false);
    const [isPrinting, setIsPrinting] = useState(false);

    // Fetch wastage details
    const fetchWastageDetails = async () => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            // Fetch wastage details
            const wastageResponse = await fetchWastageDetail(bearerToken, loginBranchID, wastageId);

            // Fetch branch details
            const branchDetailsResponse = await axios.get(
                `https://retailuat.abisibg.com/api/v1/branchdetails`,
                {
                    params: { Branchid: loginBranchID },
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );

            const wastageDetail = wastageResponse || {};
            const branchDetail = branchDetailsResponse?.data?.[0] || {};

            setWastageData(wastageDetail);
            setBranchData(branchDetail);
        } catch (error) {
            console.error('Error fetching wastage details:', error);
            Alert.alert('Error', 'Failed to load wastage details.');
        } finally {
            setLoading(false);
        }
    };

    // Calculate totals
    const calculateTotals = () => {
        if (!wastageData?.details) return { totalQty: 0 };

        let totalQty = 0;

        wastageData.details.forEach(item => {
            totalQty += parseFloat(item.Qty) || 0;
        });

        return { totalQty };
    };

    // Get unique wastage reasons for display
    const getWastageReasons = () => {
        if (!wastageData?.details) return [];
        
        const reasons = [...new Set(wastageData.details.map(item => item.WastageReasonName))];
        return reasons;
    };

    // Bluetooth printer functions
    const scanForPrinters = async (showErrorAlert = false) => {
        setIsScanning(true);
        try {
            await BLEPrinter.init();
            const devices = await BLEPrinter.getDeviceList();

            const printerDevices = devices.map(device => ({
                label: device.device_name || device.inner_mac_address,
                value: device.inner_mac_address,
                device: device
            }));

            setAvailablePrinters(printerDevices);

            if (printerDevices.length === 0 && showErrorAlert) {
                Alert.alert('No Printers Found', 'No Bluetooth printers found. Please make sure your printer is discoverable and try again.');
            }
        } catch (error) {
            console.error('Error scanning for printers:', error);
            if (showErrorAlert) {
                Alert.alert('Error', 'Failed to scan for printers. Please check Bluetooth permissions and try again.');
            }
        }
        setIsScanning(false);
    };

    const printWastage = async () => {
        if (!selectedPrinter) {
            Alert.alert('No Printer Selected', 'Please select a printer first.');
            return;
        }

        if (!wastageData || !branchData) {
            Alert.alert('Error', 'Wastage data not loaded.');
            return;
        }

        setIsPrinting(true);

        try {
            // Connect to printer
            await BLEPrinter.connectPrinter(selectedPrinter);

            // Build wastage content using the tag format
            let receiptContent = '';

            // Header
            receiptContent += '<C>ABIS EXPORTS INDIA PVT LTD\n';
            receiptContent += `${branchData.BranchName || ''}\n`;
            receiptContent += `${branchData.AreaName || ''}\n`;
            receiptContent += `${branchData.PINCODE || ''}\n`;
            receiptContent += 'PhoneNo :\n';
            receiptContent += '<<WASTAGE>></C>\n';

            // Get wastage reasons for display
            const wastageReasons = getWastageReasons();
            if (wastageReasons.length > 0) {
                receiptContent += `<C>(TYPE : ${wastageReasons.join(', ')})</C>\n`;
            }

            const businessDate = wastageData.header?.[0]?.BusinessDate 
                ? new Date(wastageData.header[0].BusinessDate).toLocaleDateString('en-GB')
                : '';
            
            receiptContent += `TranNo : ${wastageData.header?.[0]?.ISO_Number || ''}  Trans Date: ${businessDate}\n`;
            receiptContent += `Location :${branchData.BranchId}/${branchData.BranchName}\n`;
            receiptContent += `Printed On : ${new Date().toLocaleDateString('en-GB')} ${new Date().toLocaleTimeString()}\n`;
            receiptContent += '----------------------------------------\n';
            receiptContent += 'ItemCode  Item Name\n';
            receiptContent += '                                    QTY\n';
            receiptContent += '----------------------------------------\n';

            // Items
            const { totalQty } = calculateTotals();

            wastageData.details?.forEach(item => {
                const qty = parseFloat(item.Qty) || 0;

                receiptContent += `${item.ItemID}  ${item.ItemName}\n`;
                receiptContent += `                                    ${qty}\n`;
                if (item.BatchNumber) {
                    receiptContent += `Rem:${item.BatchNumber}\n`;
                }
            });

            receiptContent += '----------------------------------------\n';
            receiptContent += `TOTAL :                             ${totalQty}\n`;
            receiptContent += '\n\n\n';

            // Print the complete receipt
            await BLEPrinter.printBill(receiptContent);

            Alert.alert('Print Successful', 'The wastage has been printed successfully.');

        } catch (error) {
            console.error('Print error:', error);
            Alert.alert('Print Failed', 'Failed to print wastage: ' + error.message);
        }

        setIsPrinting(false);
    };

    // Load data and printers on component mount (scan printers silently)
    useEffect(() => {
        fetchWastageDetails();
        scanForPrinters(false);
    }, []);

    if (loading) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <ActivityIndicator size="large" color="#0000ff" />
                <Text>Loading wastage details...</Text>
            </View>
        );
    }

    if (!wastageData || !wastageData.header || !wastageData.details) {
        return (
            <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
                <Text>No wastage data found.</Text>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text style={{ color: 'blue', marginTop: 10 }}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    const header = wastageData.header[0];
    const { totalQty } = calculateTotals();
    const wastageReasons = getWastageReasons();

    return (
        <ScrollView style={styles.container}>
            <View style={styles.backButtonContainer}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-back" size={24} color="black" />
                </TouchableOpacity>
            </View>

            <Text style={styles.title}>ABIS EXPORTS INDIA PVT LTD</Text>
            <Text style={styles.center}>{branchData?.BranchName || ''}</Text>
            <Text style={styles.center}>{branchData?.AreaName || ''}</Text>
            <Text style={styles.center}>{branchData?.PINCODE || ''}</Text>
            <Text style={styles.center}>PhoneNo :</Text>
            <Text style={styles.center}>{'<<WASTAGE>>'}</Text>

            {wastageReasons.length > 0 && (
                <Text style={styles.center}>(TYPE : {wastageReasons.join(', ')})</Text>
            )}

            <View style={styles.rowSpaceBetween}>
                <Text>TranNo : {header.ISO_Number || ''}</Text>
                <Text>Trans Date: {header.BusinessDate ? new Date(header.BusinessDate).toLocaleDateString('en-GB') : ''}</Text>
            </View>
            <View style={styles.rowSpaceBetween}>
                <Text>Location: {branchData?.BranchId}/{branchData?.BranchName}</Text>
            </View>
            <View style={styles.rowRight}>
                <Text>Printed On: {new Date().toLocaleDateString('en-GB')} {new Date().toLocaleTimeString()}</Text>
            </View>

            <View style={styles.divider} />

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={[styles.cell, styles.itemCodeCell]}>ItemCode</Text>
                    <Text style={[styles.cell, styles.itemNameCell]}>Item Name</Text>
                    <Text style={[styles.cell, styles.qtyCell]}>QTY</Text>
                </View>
                {wastageData.details.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={[styles.cell, styles.itemCodeCell]}>{item.ItemID}</Text>
                        <Text style={[styles.cell, styles.itemNameCell]}>{item.ItemName}</Text>
                        <Text style={[styles.cell, styles.qtyCell]}>{parseFloat(item.Qty) || 0}</Text>
                    </View>
                ))}
            </View>

            <View style={styles.divider} />

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>TOTAL:</Text>
                <Text style={styles.bold}>{totalQty}</Text>
            </View>

            {header.Remarks && (
                <View style={styles.remarksContainer}>
                    <Text style={styles.bold}>Remarks:</Text>
                    <Text>{header.Remarks}</Text>
                </View>
            )}

            {/* Printer Controls */}
            <View style={styles.printerControlsContainer}>
                <View style={styles.printerRow}>
                    <View style={styles.dropdownContainer}>
                        <Dropdown
                            style={styles.dropdown}
                            placeholderStyle={styles.placeholderStyle}
                            selectedTextStyle={styles.selectedTextStyle}
                            data={availablePrinters}
                            maxHeight={300}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Printer"
                            value={selectedPrinter}
                            onChange={item => {
                                setSelectedPrinter(item.value);
                            }}
                        />
                    </View>

                    <TouchableOpacity
                        style={[styles.button, styles.refreshButton]}
                        onPress={() => scanForPrinters(true)}
                        disabled={isScanning}
                    >
                        {isScanning ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <Icon name="refresh" size={20} color="#fff" />
                        )}
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.button, styles.printButton]}
                        onPress={printWastage}
                        disabled={isPrinting || !selectedPrinter}
                    >
                        {isPrinting ? (
                            <ActivityIndicator size="small" color="#fff" />
                        ) : (
                            <>
                                <Icon name="print" size={20} color="#fff" />
                                <Text style={styles.buttonText}>Print</Text>
                            </>
                        )}
                    </TouchableOpacity>
                </View>
            </View>
        </ScrollView>
    );
};

export default ViewWastageScreen;

const styles = StyleSheet.create({
    backButtonContainer: {
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 10,
        padding: 8,
    },
    container: {
        padding: 16,
        backgroundColor: '#fff',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        textAlign: 'center',
        marginTop: 40,
    },
    center: {
        textAlign: 'center',
        marginVertical: 2,
    },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginVertical: 4,
    },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
        marginBottom: 8,
    },
    table: {
        borderWidth: 1,
        borderColor: '#000',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#000',
        minHeight: 40,
        alignItems: 'center',
    },
    cell: {
        padding: 8,
        textAlign: 'center',
        borderRightWidth: 1,
        borderRightColor: '#000',
        fontSize: 12,
    },
    itemCodeCell: {
        flex: 1.5,
        textAlign: 'left',
    },
    itemNameCell: {
        flex: 3,
        textAlign: 'left',
    },
    qtyCell: {
        flex: 1,
        textAlign: 'right',
        borderRightWidth: 0,
    },
    bold: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#000',
    },
    remarksContainer: {
        marginTop: 16,
        padding: 8,
        backgroundColor: '#f5f5f5',
        borderRadius: 4,
    },
    printerControlsContainer: {
        marginTop: 20,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#ccc',
    },
    printerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 10,
    },
    dropdownContainer: {
        flex: 1,
    },
    dropdown: {
        height: 50,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
    },
    placeholderStyle: {
        fontSize: 16,
        color: '#999',
    },
    selectedTextStyle: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 16,
        flexDirection: 'row',
        gap: 8,
    },
    refreshButton: {
        backgroundColor: '#007bff',
        width: 50,
    },
    printButton: {
        backgroundColor: '#28a745',
        minWidth: 80,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
});
